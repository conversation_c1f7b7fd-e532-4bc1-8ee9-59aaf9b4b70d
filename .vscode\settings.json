{
  "C_Cpp_Runner.cCompilerPath": "D:/MinGw/bin/gcc.exe",
  "C_Cpp_Runner.cppCompilerPath": "D:/MinGw/bin/g++.exe",
  "C_Cpp_Runner.debuggerPath": "D:/MinGw/bin/gdb.exe",
  "C_Cpp_Runner.cStandard": "c17",
  "C_Cpp_Runner.cppStandard": "c++17",
  "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat",
  "C_Cpp_Runner.useMsvc": false,
  "C_Cpp_Runner.warnings": [
    "-Wall",
    "-Wextra",
    "-Wpedantic",
    "-Wshadow",
    "-Wformat=2",
    "-Wcast-align",
    "-Wconversion",
    "-Wsign-conversion",
    "-Wnull-dereference"
  ],
  "C_Cpp_Runner.msvcWarnings": [
    "/W4",
    "/permissive-",
    "/w14242",
    "/w14287",
    "/w14296",
    "/w14311",
    "/w14826",
    "/w44062",
    "/w44242",
    "/w14905",
    "/w14906",
    "/w14263",
    "/w44265",
    "/w14928"
  ],
  "C_Cpp_Runner.enableWarnings": true,
  "C_Cpp_Runner.warningsAsError": false,
  "C_Cpp_Runner.compilerArgs": [],
  "C_Cpp_Runner.linkerArgs": [],
  "C_Cpp_Runner.includePaths": [],
  "C_Cpp_Runner.includeSearch": ["*", "**/*"],
  "C_Cpp_Runner.excludeSearch": [
    "**/build",
    "**/build/**",
    "**/.*",
    "**/.*/**",
    "**/.vscode",
    "**/.vscode/**"
  ],
  "C_Cpp_Runner.useAddressSanitizer": false,
  "C_Cpp_Runner.useUndefinedSanitizer": false,
  "C_Cpp_Runner.useLeakSanitizer": false,
  "C_Cpp_Runner.showCompilationTime": false,
  "C_Cpp_Runner.useLinkTimeOptimization": false,
  "C_Cpp_Runner.msvcSecureNoWarnings": false,

  // C++ 特定设置
  "C_Cpp.default.cppStandard": "c++17",
  "C_Cpp.default.cStandard": "c17",
  "C_Cpp.default.compilerPath": "D:/MinGw/bin/g++.exe",
  "C_Cpp.default.intelliSenseMode": "windows-gcc-x86",

  // 编辑器设置
  "editor.formatOnSave": true,
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,

  // 文件设置
  "files.associations": {
    "*.cpp": "cpp",
    "*.h": "c",
    "*.hpp": "cpp",
    "ostream": "cpp"
  },

  // 终端设置
  "terminal.integrated.defaultProfile.windows": "PowerShell"
}
