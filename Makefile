# C++ 项目 Makefile
CXX = D:/MinGw/bin/g++.exe
CXXFLAGS = -std=c++17 -Wall -Wextra -g -fdiagnostics-color=always
TARGET_DIR = build
SOURCE_DIR = .

# 默认目标
all: setup carritos

# 创建构建目录
setup:
	@if not exist $(TARGET_DIR) mkdir $(TARGET_DIR)

# 编译 carritos
carritos: setup
	$(CXX) $(CXXFLAGS) ch02/carritos.cpp -o $(TARGET_DIR)/carritos.exe

# 运行 carritos
run-carritos: carritos
	$(TARGET_DIR)/carritos.exe

# 清理构建文件
clean:
	@if exist $(TARGET_DIR) rmdir /s /q $(TARGET_DIR)
	@if exist ch02\*.exe del ch02\*.exe

# 编译当前目录下的所有 .cpp 文件
compile-all: setup
	@for %%f in (*.cpp) do $(CXX) $(CXXFLAGS) %%f -o $(TARGET_DIR)/%%~nf.exe
	@for %%f in (*/*.cpp) do $(CXX) $(CXXFLAGS) %%f -o $(TARGET_DIR)/%%~nf.exe

# 帮助信息
help:
	@echo 可用的目标:
	@echo   all          - 编译所有项目
	@echo   carritos     - 编译 carritos 程序
	@echo   run-carritos - 编译并运行 carritos
	@echo   compile-all  - 编译所有 .cpp 文件
	@echo   clean        - 清理构建文件
	@echo   help         - 显示此帮助信息

.PHONY: all setup carritos run-carritos clean compile-all help
