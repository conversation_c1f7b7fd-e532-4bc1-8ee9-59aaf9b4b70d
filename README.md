# C++ 学习项目

这是一个配置完整的 C++ 开发环境，适用于学习和开发 C++ 程序。

## 环境配置

### 编译器
- **编译器**: MinGW GCC 6.3.0
- **路径**: `D:/MinGw/bin/g++.exe`
- **C++ 标准**: C++17
- **调试器**: GDB

### VS Code 配置
- **IntelliSense**: 已配置自动补全和语法检查
- **编译任务**: 按 `Ctrl+Shift+B` 编译当前文件
- **调试**: 按 `F5` 开始调试

## 项目结构

```
learnC++/
├── .vscode/                 # VS Code 配置文件
│   ├── c_cpp_properties.json   # C++ 属性配置
│   ├── launch.json             # 调试配置
│   ├── settings.json           # 编辑器设置
│   └── tasks.json              # 编译任务
├── ch02/                    # 第二章示例
│   └── carritos.cpp            # 胡萝卜示例程序
├── build/                   # 编译输出目录
├── Makefile                 # Make 构建脚本
└── README.md               # 项目说明

```

## 使用方法

### 1. 编译和运行
```bash
# 使用 VS Code 任务
Ctrl+Shift+B  # 编译当前文件
F5            # 调试当前文件

# 使用 Makefile
make carritos      # 编译 carritos
make run-carritos  # 编译并运行 carritos
make compile-all   # 编译所有 .cpp 文件
make clean         # 清理构建文件
```

### 2. 调试配置
- **调试当前文件**: 自动编译并调试当前打开的 .cpp 文件
- **调试 carritos**: 专门用于调试 carritos.cpp 程序

### 3. 编译器选项
- `-std=c++17`: 使用 C++17 标准
- `-Wall -Wextra`: 启用所有警告
- `-g`: 生成调试信息
- `-fdiagnostics-color=always`: 彩色错误输出

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+Shift+B` | 编译当前文件 |
| `F5` | 开始调试 |
| `Ctrl+F5` | 运行而不调试 |
| `F9` | 设置/取消断点 |
| `F10` | 单步执行 |
| `F11` | 步入函数 |

## 注意事项

1. 确保 MinGW 已正确安装在 `D:/MinGw/` 目录
2. 如果编译器路径不同，请修改 `.vscode/c_cpp_properties.json` 中的路径
3. 编译后的可执行文件会生成在源文件同目录或 `build/` 目录中
4. 使用外部控制台运行程序，便于查看输出和输入

## 故障排除

### 常见问题
1. **编译错误**: 检查编译器路径是否正确
2. **调试失败**: 确保 GDB 路径配置正确
3. **IntelliSense 不工作**: 重新加载 VS Code 窗口

### 重新配置
如果需要重新配置环境，可以删除 `.vscode` 目录并重新生成配置文件。
