{"version": "0.2.0", "configurations": [{"name": "C/C++: 调试当前文件", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "${fileDirname}", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "MIMode": "gdb", "miDebuggerPath": "D:/MinGw/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe 生成活动文件"}, {"name": "C/C++: 调试 carritos", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "f:/learnC++/ch02", "program": "f:/learnC++/ch02/carritos.exe", "MIMode": "gdb", "miDebuggerPath": "D:/MinGw/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "f:/learnC++/ch02", "program": "f:/learnC++/ch02/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "D:/MinGw/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}